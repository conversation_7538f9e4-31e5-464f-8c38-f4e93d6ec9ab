import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Wallet, Receipt, CreditCard, AlertCircle, Settings, Loader2 } from "lucide-react"
import { toast } from "sonner"
import { getPaymentsByMembership, updatePaymentDetails, confirmPayment } from "@/services/billing/payment-actions"
import { format, isSameMonth, parseISO } from "date-fns"
import { ptBR } from "date-fns/locale"
import { PaymentStatusProps, statusMensalidadeText, statusMensalidadeClassName } from "../types/types"
import { LoadingState } from "./LoadingState"
import { useUserMetadata } from "@/hooks/user/Auth"
import { useUserRole } from "@/hooks/user/Permissions"
import { useState, useEffect } from "react"
import { PaymentManagementModal } from "./PaymentManagementModal"
import { useRouter } from "next/navigation"

// Função para determinar se o plano é recorrente ou único
const isRecurringPlan = (plan: any): boolean => {
  // Se o plano tem pricing_config, usar essa estrutura
  if (plan.pricing_config) {
    const pricingConfig = plan.pricing_config
    const tipo = pricingConfig.tipo || pricingConfig.type || 'recurring'
    return tipo === 'recurring'
  }

  // Fallback: verificar pelo billing_period
  return plan.billing_period !== 'Pagamento único'
}

export function PaymentStatus({
  pagamentoInfo,
  valorMensalidade,
  loading,
  currentPlan,
  userId,
  onUpdate,
}: PaymentStatusProps) {
  const { metadata } = useUserMetadata()
  const { isAdmin } = useUserRole()
  const router = useRouter()
  const [showManagementModal, setShowManagementModal] = useState(false)
  const [loadingCheckout, setLoadingCheckout] = useState(false)
  type ModalPaymentData = {
    id: string
    amount: number
    currency: string
    status: string
    payment_method?: string
    paid_at?: string
    due_date?: string
    description?: string
  } | null

  const [paymentData, setPaymentData] = useState<ModalPaymentData>(null)
  const [fetchingPayment, setFetchingPayment] = useState(false)
  const [hasAwaitingConfirmation, setHasAwaitingConfirmation] = useState(false)

  // Verificar se o usuário atual é o dono do perfil
  const isOwnProfile = metadata?.id === userId

  // Verificar se há pagamentos aguardando confirmação
  const checkAwaitingConfirmation = async () => {
    if (!currentPlan?.membership_id) return

    try {
      const result = await getPaymentsByMembership({ membershipId: currentPlan.membership_id, limit: 10 })
      if (result.success) {
        const payments = (result.data || []) as any[]
        const hasAwaiting = payments.some((payment: any) => payment.status === 'awaiting_confirmation')
        setHasAwaitingConfirmation(hasAwaiting)
      }
    } catch (error) {
      console.error('Erro ao verificar pagamentos aguardando confirmação:', error)
    }
  }

  // Executar verificação quando o componente montar ou currentPlan mudar
  useEffect(() => {
    checkAwaitingConfirmation()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPlan?.membership_id])

  if (loading) {
    return <LoadingState height="h-40" />
  }

  // Estado vazio - quando não há plano vinculado
  if (!currentPlan) {
    return (
      <Card className="p-6 bg-white dark:bg-slate-800">
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-full mb-4">
            <AlertCircle className="w-8 h-8 text-gray-500 dark:text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Nenhum plano ativo
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Você não possui um plano ativo no momento. Entre em contato com a academia para ativar seu plano.
          </p>
        </div>
      </Card>
    )
  }

  const getMensalidadeStatusText = () => {
    // Se há pagamentos aguardando confirmação e o status é pendente, mostrar "Aguardando Confirmação"
    if (hasAwaitingConfirmation && pagamentoInfo.statusMensalidade === 'pendente') {
      return 'Aguardando Confirmação'
    }
    return statusMensalidadeText[pagamentoInfo.statusMensalidade] || ''
  }

  const getStatusClassName = () => {
    // Se há pagamentos aguardando confirmação e o status é pendente, usar estilo azul
    if (hasAwaitingConfirmation && pagamentoInfo.statusMensalidade === 'pendente') {
      return 'text-blue-600 border-blue-600'
    }
    return statusMensalidadeClassName[pagamentoInfo.statusMensalidade] || ''
  }

  const formatarProximoVencimento = () => {
    if (!pagamentoInfo.proximoVencimento) return 'Não definido'
    return format(pagamentoInfo.proximoVencimento, "dd/MM/yyyy")
  }

  /**
   * Determina o mês referente à mensalidade exibida.
   *
   * Lógica baseada no conceito de ciclos de mensalidade:
   * - Cada mensalidade tem um período de vigência (ex: 25/jul a 24/ago)
   * - O nome da mensalidade corresponde ao mês de INÍCIO do ciclo
   * - Para usuários "em dia": mostrar o mês do último ciclo pago
   * - Para usuários pendentes/atrasados: mostrar o mês do ciclo em atraso
   *
   * Casos especiais:
   * - Se não há próximo vencimento (pagamento antecipado sem próximo ciclo): usar mês atual
   * - Se o próximo vencimento é no futuro próximo: pode ser pagamento antecipado
   */
  const getMesAtual = () => {
    if (!pagamentoInfo.proximoVencimento) {
      return format(new Date(), "MMMM", { locale: ptBR })
    }

    const isEmDia = pagamentoInfo.statusMensalidade === 'em_dia'
    const hoje = new Date()
    const proximoVencimento = pagamentoInfo.proximoVencimento

    if (isEmDia) {
      // Verificar se é um caso especial onde não há próximo pagamento pendente
      // Isso indica que o usuário pagou antecipadamente e não há cobrança futura criada
      // Neste caso, o próximo vencimento representa o período que foi pago

      // Se o próximo vencimento é no mês atual ou próximo E o dia ainda não passou
      const diaVencimento = proximoVencimento.getDate()
      const diaHoje = hoje.getDate()
      const mesVencimento = proximoVencimento.getMonth()
      const mesAtual = hoje.getMonth()
      const anoVencimento = proximoVencimento.getFullYear()
      const anoAtual = hoje.getFullYear()

      // Caso especial: vencimento no mesmo mês e ainda não passou o dia
      if (anoVencimento === anoAtual && mesVencimento === mesAtual && diaVencimento >= diaHoje) {
        // Pagamento antecipado no mesmo mês - mostrar o mês do vencimento
        return format(proximoVencimento, "MMMM", { locale: ptBR })
      }
      // Caso especial: vencimento no próximo mês
      else if (anoVencimento === anoAtual && mesVencimento === mesAtual + 1) {
        // Pagamento antecipado para o próximo mês - mostrar o mês do vencimento
        return format(proximoVencimento, "MMMM", { locale: ptBR })
      }
      // Caso normal: ciclo regular
      else {
        // Mostrar o mês anterior ao próximo vencimento (ciclo que foi pago)
        const mesAnterior = new Date(proximoVencimento)
        mesAnterior.setMonth(mesAnterior.getMonth() - 1)
        return format(mesAnterior, "MMMM", { locale: ptBR })
      }
    } else {
      // Para pendentes/atrasados, mostrar o mês do ciclo em atraso
      const mesAnterior = new Date(proximoVencimento)
      mesAnterior.setMonth(mesAnterior.getMonth() - 1)
      return format(mesAnterior, "MMMM", { locale: ptBR })
    }
  }

  const isPlanRecurring = isRecurringPlan(currentPlan)

  const handlePaymentCheckout = async () => {
    if (!currentPlan?.membership_id) {
      toast.error('Matrícula não encontrada')
      return
    }

    if (loadingCheckout) return

    try {
      setLoadingCheckout(true)

      // Buscar todos os pagamentos pendentes para esta membership
      const result = await getPaymentsByMembership({ membershipId: currentPlan.membership_id, limit: 20 })

      if (!result.success) {
        toast.error(result.errors?._form || 'Erro ao buscar pagamento')
        return
      }

      const payments = (result.data || []) as any[]

      if (payments.length === 0) {
        toast.error('Nenhum pagamento pendente encontrado')
        return
      }

      // Filtrar apenas pagamentos pendentes
      const pendingPayments = payments.filter(p => p.status === 'pending')

      if (pendingPayments.length === 0) {
        toast.error('Nenhum pagamento pendente encontrado')
        return
      }

      // Ordenar pagamentos pendentes por data de vencimento (mais próximo primeiro)
      const sortedPendingPayments = pendingPayments.sort((a, b) => {
        const dateA = new Date(a.due_date)
        const dateB = new Date(b.due_date)
        return dateA.getTime() - dateB.getTime()
      })

      // Pegar o pagamento com vencimento mais próximo (incluindo vencidos)
      const targetPayment = sortedPendingPayments[0]

      if (!targetPayment) {
        toast.error('Nenhum pagamento pendente encontrado')
        return
      }

      // Redirecionar para a página de checkout
      router.push(`/checkout/${targetPayment.id}`)

    } catch (error) {
      console.error('Erro ao redirecionar para checkout:', error)
      toast.error('Erro ao acessar checkout')
    } finally {
      setLoadingCheckout(false)
    }
  }

  const handleManagePayment = async () => {
    if (!currentPlan?.membership_id) {
      toast.error('Matrícula não encontrada')
      return
    }

    try {
      setFetchingPayment(true)

      // Buscamos um conjunto maior de pagamentos para poder filtrar localmente
      const result = await getPaymentsByMembership({ membershipId: currentPlan.membership_id, limit: 20 })

      if (!result.success) {
        toast.error(result.errors?._form || 'Erro ao buscar pagamento')
        return
      }

      const payments = (result.data || []) as any[]

      if (payments.length === 0) {
        toast.error('Nenhum pagamento encontrado')
        return
      }

      // Determinar o ciclo que queremos gerenciar
      // Sempre usar a data do próximo vencimento para garantir consistência
      let targetCycleDate: Date
      if (!pagamentoInfo.proximoVencimento) {
        targetCycleDate = new Date()
      } else {
        // Para qualquer status, usar a data do próximo vencimento
        // Isso garante que sempre gerenciamos o ciclo correto
        targetCycleDate = pagamentoInfo.proximoVencimento
      }

      // Procurar pagamento cujo due_date coincide com o ciclo desejado
      const paymentMatch = payments.find((p) => {
        if (!p.due_date) return false
        const due = typeof p.due_date === 'string' ? parseISO(p.due_date) : new Date(p.due_date)
        return isSameMonth(due, targetCycleDate) && due.getFullYear() === targetCycleDate.getFullYear()
      }) || payments[0] // fallback para o primeiro

      setPaymentData({
        id: paymentMatch.id,
        amount: paymentMatch.amount,
        currency: paymentMatch.currency,
        status: paymentMatch.status,
        payment_method: paymentMatch.payment_method,
        paid_at: paymentMatch.paid_at,
        due_date: paymentMatch.due_date,
        description: paymentMatch.description
      })

      setShowManagementModal(true)
    } finally {
      setFetchingPayment(false)
    }
  }

  const renderActionButton = () => {
    if (isAdmin) {
      // Admins veem botão de gerenciar
      return (
        <Button
          className="gap-2"
          onClick={handleManagePayment}
          disabled={fetchingPayment}
        >
          <Settings className="w-4 h-4" />
          Gerenciar
        </Button>
      )
    } else if (isOwnProfile) {
      // Verificar se deve mostrar o botão de pagar
      const isEmDia = pagamentoInfo.statusMensalidade === 'em_dia'
      const isAguardandoConfirmacao = pagamentoInfo.statusMensalidade === 'pendente' && hasAwaitingConfirmation

      // Não mostrar botão quando está em dia ou aguardando confirmação
      if (isEmDia || isAguardandoConfirmacao) {
        return null
      }

      // Dono do perfil vê botão de pagar
      return (
        <Button
          className="gap-2"
          onClick={handlePaymentCheckout}
          disabled={loadingCheckout}
        >
          {loadingCheckout ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Receipt className="w-4 h-4" />
          )}
          {loadingCheckout
            ? 'Carregando...'
            : (isPlanRecurring ? 'Pagar Mensalidade' : 'Efetuar Pagamento')
          }
        </Button>
      )
    }

    // Instrutores e outros não veem botão
    return null
  }

  const handleSavePayment = async (updatedData: any) => {
    if (!paymentData) return
    try {
      const result = await updatePaymentDetails({
        paymentId: paymentData.id,
        ...updatedData,
      })
      if (!result.success) {
        toast.error(result.errors?._form || 'Erro ao atualizar pagamento')
        return
      }
      toast.success('Pagamento atualizado com sucesso')
      setShowManagementModal(false)
      checkAwaitingConfirmation() // Recarregar verificação
      onUpdate?.()
    } catch (error) {
      toast.error('Erro inesperado ao atualizar pagamento')
    }
  }

  const handleConfirmPayment = async (paymentId: string) => {
    try {
      const result = await confirmPayment({
        paymentId: paymentId,
        novoStatus: 'paid'
      })
      if (!result.success) {
        toast.error(result.errors?._form || 'Erro ao confirmar pagamento')
        return
      }
      toast.success('Pagamento confirmado com sucesso')
      setShowManagementModal(false)
      checkAwaitingConfirmation() // Recarregar verificação
      onUpdate?.()
    } catch (error) {
      toast.error('Erro inesperado ao confirmar pagamento')
    }
  }

  // Layout para pagamento recorrente
  if (isPlanRecurring) {
    return (
      <Card className="p-6 bg-white dark:bg-slate-800">
        <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
          <div className="flex-shrink-0">
            <div className="p-4 bg-primary/10 rounded-full">
              <Wallet className="w-8 h-8 text-primary" />
            </div>
          </div>

          <div className="flex-grow space-y-4">
            <div>
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Mensalidade de {getMesAtual()}
                </h3>
                <Badge
                  variant="outline"
                  className={getStatusClassName()}
                >
                  {getMensalidadeStatusText()}
                </Badge>
              </div>

              {pagamentoInfo.statusMensalidade === 'em_dia' && (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Você está em dia! {pagamentoInfo.proximoVencimento ? `Próxima cobrança será em ${formatarProximoVencimento()}.` : 'Nenhuma cobrança pendente.'}
                </p>
              )}

              {pagamentoInfo.statusMensalidade === 'pendente' && (
                <p className=" text-gray-500 dark:text-gray-400 text-sm">
                  {hasAwaitingConfirmation
                    ? 'Pagamento realizado e aguardando confirmação do administrador.'
                    : `Pagamento pendente. Vencimento em ${formatarProximoVencimento()}.`
                  }
                </p>
              )}

              {pagamentoInfo.statusMensalidade === 'atrasado' && (
                <p className=" text-gray-500 dark:text-gray-400 text-sm">
                  Pagamento em atraso! Venceu em {formatarProximoVencimento()}.
                </p>
              )}
            </div>

            <div className="flex items-baseline gap-2">
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                R$ {valorMensalidade.toFixed(2)}
              </span>
              <span className="text-sm text-gray-500">/{currentPlan.billing_period}</span>
            </div>
          </div>

          <div className="flex-shrink-0">
            {renderActionButton()}
          </div>
        </div>

        {/* Modal de Gerenciamento de Pagamento */}
        <PaymentManagementModal
          isOpen={showManagementModal}
          onClose={() => setShowManagementModal(false)}
          paymentData={paymentData as any}
          onSave={handleSavePayment}
          onConfirm={handleConfirmPayment}
          onUpdate={onUpdate}
        />
      </Card>
    )
  }

  // Layout para pagamento único
  return (
    <Card className="p-6 bg-white dark:bg-slate-800">
      <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
        <div className="flex-shrink-0">
          <div className="p-4 bg-blue-100 dark:bg-blue-900/30 rounded-full">
            <CreditCard className="w-8 h-8 text-blue-600 dark:text-blue-400" />
          </div>
        </div>

        <div className="flex-grow space-y-4">
          <div>
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Pagamento Único
              </h3>
              <Badge
                variant="outline"
                className={getStatusClassName()}
              >
                {getMensalidadeStatusText()}
              </Badge>
            </div>
            {/* <p className="text-sm text-gray-500">
              {pagamentoInfo.proximoVencimento
                ? `Vencimento em ${formatarProximoVencimento()}`
                : 'Pagamento único do plano'
              }
            </p> */}

            {pagamentoInfo.statusMensalidade === 'pendente' && (
              <p className=" text-gray-500 text-sm">
                {hasAwaitingConfirmation
                  ? 'Pagamento realizado e aguardando confirmação do administrador.'
                  : `Pagamento pendente. Vencimento em ${formatarProximoVencimento()}.`
                }
              </p>
            )}

            {pagamentoInfo.statusMensalidade === 'atrasado' && (
              <p className=" text-gray-500 text-sm">
                Pagamento em atraso! Venceu em {formatarProximoVencimento()}.
              </p>
            )}
          </div>

          <div className="flex items-baseline gap-2">
            <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              R$ {valorMensalidade.toFixed(2)}
            </span>
            <span className="text-sm text-gray-500">pagamento único</span>
          </div>
        </div>

        <div className="flex-shrink-0">
          {renderActionButton()}
        </div>
      </div>

      {/* Modal de Gerenciamento de Pagamento */}
      <PaymentManagementModal
        isOpen={showManagementModal}
        onClose={() => setShowManagementModal(false)}
        paymentData={paymentData as any}
        onSave={handleSavePayment}
        onConfirm={handleConfirmPayment}
        onUpdate={onUpdate}
      />
    </Card>
  )
}
