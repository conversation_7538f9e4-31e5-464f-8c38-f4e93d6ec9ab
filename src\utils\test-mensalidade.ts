import { determinarMensalidadePaga } from './mensalidade-calculator'

// Teste com dados do F<PERSON><PERSON> Torres
console.log('=== Teste Fabio <PERSON> ===')

// Cenário: Fabio pagou no dia 25/07/2025 uma mensalidade com vencimento 25/07/2025
// Data de matrícula: 20/05/2025 (mas membership start: 25/07/2025)
const resultadoFabio = determinarMensalidadePaga({
  data_pagamento: '2025-07-25T03:00:00.000Z',
  tipo_vencimento: 'matricula',
  data_matricula: '2025-07-25', // Usando membership start como referência
  eh_primeiro_pagamento: false
})

console.log('Resultado Fabio:', resultadoFabio)

// Teste com Maria Pinto
console.log('\n=== Teste Maria Pinto ===')

// Cenário: Maria pagou no dia 25/07/2025 uma mensalidade com vencimento 15/08/2025 (antecipado)
const resultadoMaria = determinarMensalidadePaga({
  data_pagamento: '2025-07-25T03:00:00.000Z',
  tipo_vencimento: 'fixo',
  dia_vencimento_fixo: 15,
  eh_primeiro_pagamento: false
})

console.log('Resultado Maria:', resultadoMaria)

// Teste cenários diversos
console.log('\n=== Testes Diversos ===')

// Primeiro pagamento
const primeiroPagamento = determinarMensalidadePaga({
  data_pagamento: '2025-07-25',
  tipo_vencimento: 'fixo',
  dia_vencimento_fixo: 15,
  eh_primeiro_pagamento: true
})
console.log('Primeiro pagamento (25/07, venc. dia 15):', primeiroPagamento)

// Pagamento antecipado
const pagamentoAntecipado = determinarMensalidadePaga({
  data_pagamento: '2025-07-10',
  tipo_vencimento: 'fixo',
  dia_vencimento_fixo: 15,
  eh_primeiro_pagamento: false
})
console.log('Pagamento antecipado (10/07, venc. dia 15):', pagamentoAntecipado)

// Pagamento atrasado
const pagamentoAtrasado = determinarMensalidadePaga({
  data_pagamento: '2025-07-20',
  tipo_vencimento: 'fixo',
  dia_vencimento_fixo: 15,
  eh_primeiro_pagamento: false
})
console.log('Pagamento atrasado (20/07, venc. dia 15):', pagamentoAtrasado)
